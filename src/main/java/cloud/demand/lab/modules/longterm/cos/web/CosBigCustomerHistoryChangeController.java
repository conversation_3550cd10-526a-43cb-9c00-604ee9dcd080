package cloud.demand.lab.modules.longterm.cos.web;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputBigCustomerChangeDO;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryBigCustomerHistoryChangeReq;
import cloud.demand.lab.modules.longterm.cos.web.req.SaveBigCustomerHistoryChangeReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryBigCustomerHistoryChangeResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.SaveBigCustomerHistoryChangeResp;
import com.pugwoo.dbhelper.DBHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import java.util.List;

/**
 * 负责大客户历史变动数据增删改查
 */
@JsonrpcController("/cos-longterm-predict")
@Slf4j
public class CosBigCustomerHistoryChangeController {

    @Resource
    private DBHelper cdLabDbHelper;

    /**
     * 查询大客户历史变动数据
     * @param req 查询请求，必须包含categoryId，taskId可选（为0时查询默认数据）
     * @return 大客户历史变动数据列表
     */
    @RequestMapping
    public QueryBigCustomerHistoryChangeResp queryBigCustomerHistoryChange(@JsonrpcParam QueryBigCustomerHistoryChangeReq req) {
        log.info("查询大客户历史变动数据，请求参数：{}", req);

        if (req == null || req.getCategoryId() == null) {
            throw new RuntimeException("方案id(categoryId)不能为空");
        }

        String whereSql;
        Object[] params;

        if (req.getTaskId() != null && req.getTaskId() != 0) {
            // 查询指定任务的数据
            whereSql = "where category_id = ? and task_id = ?";
            params = new Object[]{req.getCategoryId(), req.getTaskId()};
        } else {
            // 查询默认数据（taskId为0）
            whereSql = "where category_id = ? and task_id = 0";
            params = new Object[]{req.getCategoryId()};
        }

        List<CosLongtermPredictInputBigCustomerChangeDO> dataList = cdLabDbHelper.getAll(
            CosLongtermPredictInputBigCustomerChangeDO.class,
            whereSql + " order by start_date, customer_name",
            params
        );

        QueryBigCustomerHistoryChangeResp resp = new QueryBigCustomerHistoryChangeResp();
        resp.setDataList(dataList);

        log.info("查询大客户历史变动数据完成，返回{}条记录", dataList.size());
        return resp;
    }

    /**
     * 保存大客户历史变动数据
     * 采用覆盖策略：先删除已存在的数据，再全量插入新数据
     * @param req 保存请求，必须包含categoryId，taskId可选（未传时填0）
     * @return 保存结果
     */
    @RequestMapping
    public SaveBigCustomerHistoryChangeResp saveBigCustomerHistoryChange(@JsonrpcParam SaveBigCustomerHistoryChangeReq req) {
        log.info("保存大客户历史变动数据，请求参数：{}", req);

        if (req == null || req.getCategoryId() == null) {
            throw new RuntimeException("方案id(categoryId)不能为空");
        }

        if (req.getDataList() == null) {
            throw new RuntimeException("数据列表不能为空");
        }

        Long categoryId = req.getCategoryId();
        Long taskId = req.getTaskId() != null ? req.getTaskId() : 0L;

        // 1. 先删除已存在的数据
        String deleteSql = "where category_id = ? and task_id = ?";
        int deletedCount = cdLabDbHelper.delete(CosLongtermPredictInputBigCustomerChangeDO.class, deleteSql, categoryId, taskId);
        log.info("删除已存在的数据，删除{}条记录", deletedCount);

        // 2. 设置数据的categoryId和taskId，然后批量插入
        if (!req.getDataList().isEmpty()) {
            for (CosLongtermPredictInputBigCustomerChangeDO data : req.getDataList()) {
                data.setCategoryId(categoryId);
                data.setTaskId(taskId);
                // 清空ID，让数据库自动生成
                data.setId(null);
            }

            int insertedCount = cdLabDbHelper.insertBatchWithoutReturnId(req.getDataList());
            log.info("批量插入新数据，插入{}条记录", insertedCount);
        }

        log.info("保存大客户历史变动数据完成");

        SaveBigCustomerHistoryChangeResp resp = new SaveBigCustomerHistoryChangeResp()
                return resp;
        return "success";
    }
}
